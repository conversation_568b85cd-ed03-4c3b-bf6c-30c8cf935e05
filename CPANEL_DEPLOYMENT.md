# cPanel Deployment Guide

This guide will help you deploy your Canva Clone Next.js application to cPanel hosting.

## 📋 Prerequisites

Before deploying to cPanel, ensure you have:

- ✅ cPanel hosting account with Node.js support (most modern cPanel hosts support this)
- ✅ Domain name configured and pointing to your cPanel hosting
- ✅ MySQL database access (most cPanel hosting uses MySQL instead of PostgreSQL)
- ✅ SSL certificate (recommended for production)

## 🚀 Quick Start

### 1. Initial Setup

Run the setup script to prepare your project for cPanel deployment:

```bash
npm run cpanel:setup
```

This script will:
- Check for required files
- Create `.env.production` from template
- Install necessary dependencies
- Set up required directories

### 2. Configure Environment Variables

Edit `.env.production` with your actual values:

```bash
# Your domain
NEXT_PUBLIC_APP_URL=https://yourdomain.com

# Enable cPanel deployment
CPANEL_DEPLOY=true

# Database (MySQL for cPanel)
DB_HOST=localhost
DB_NAME=your_cpanel_database_name
DB_USER=your_cpanel_database_user
DB_PASSWORD=your_cpanel_database_password

# Add your API keys
TOGETHER_API_KEY=your_together_ai_key
CLIPDROP_API_KEY=your_clipdrop_key
# ... other API keys
```

### 3. Check Compatibility

Run the compatibility check:

```bash
npm run cpanel:check
```

This will verify your project is ready for cPanel deployment.

### 4. Build for Production

Build your project for cPanel:

```bash
npm run build:cpanel
```

### 5. Deploy

Generate deployment files:

```bash
npm run deploy:cpanel
```

This creates an `out` directory with all files ready for upload.

## 📁 File Upload Instructions

### Method 1: File Manager (Recommended for beginners)

1. **Login to cPanel**
2. **Open File Manager**
3. **Navigate to public_html** (or your domain's document root)
4. **Upload all files** from the `out` directory to `public_html`
5. **Extract if uploaded as ZIP** (some prefer to ZIP the out folder first)

### Method 2: FTP/SFTP

1. **Connect to your hosting** via FTP/SFTP
2. **Navigate to public_html**
3. **Upload all files** from the `out` directory
4. **Ensure .htaccess is uploaded** (some FTP clients hide dot files)

### Method 3: Git Deployment (Advanced)

If your host supports Git:

1. **Push your code** to a Git repository
2. **Clone on server** (if supported)
3. **Run build commands** on server
4. **Copy out directory** to public_html

## 🗄️ Database Setup

### 1. Create MySQL Database

In cPanel:
1. Go to **MySQL Databases**
2. **Create a new database**
3. **Create a database user**
4. **Add user to database** with all privileges
5. **Note the connection details**

### 2. Update Database Configuration

Update your `.env.production`:

```bash
DB_HOST=localhost
DB_NAME=yourusername_dbname
DB_USER=yourusername_dbuser
DB_PASSWORD=your_secure_password
```

### 3. Run Database Migrations

If you need to set up tables, you can:
- Use phpMyAdmin to import SQL files
- Run migrations if your host supports Node.js execution
- Use database seeding scripts

## 🔧 Configuration Details

### Next.js Configuration

The project is configured for static export when `CPANEL_DEPLOY=true`:

```javascript
// next.config.mjs
output: process.env.CPANEL_DEPLOY === 'true' ? 'export' : undefined,
images: {
  unoptimized: true // Required for static export
}
```

### Apache Configuration

The `.htaccess` file handles:
- URL rewriting for client-side routing
- Security headers
- CORS for API requests
- Static asset caching
- HTTPS redirects

### Environment Variables

Key environment variables for cPanel:

```bash
# Deployment
CPANEL_DEPLOY=true
NEXT_PUBLIC_APP_URL=https://yourdomain.com

# Database (MySQL)
DB_HOST=localhost
DB_NAME=your_database
DB_USER=your_user
DB_PASSWORD=your_password

# Security
AUTH_SECRET=your_secure_random_string

# API Keys
TOGETHER_API_KEY=your_key
CLIPDROP_API_KEY=your_key
UNSPLASH_ACCESS_KEY=your_key
```

## 🔍 Troubleshooting

### Common Issues

#### 1. **404 Errors on Routes**
- **Cause**: .htaccess not uploaded or not working
- **Solution**: Ensure .htaccess is in the root directory and mod_rewrite is enabled

#### 2. **Images Not Loading**
- **Cause**: Image optimization enabled
- **Solution**: Verify `unoptimized: true` in next.config.mjs

#### 3. **API Errors**
- **Cause**: Environment variables not set
- **Solution**: Check .env.production configuration

#### 4. **Database Connection Errors**
- **Cause**: Wrong database credentials or host
- **Solution**: Verify database settings in cPanel

#### 5. **Build Errors**
- **Cause**: Server-side features in static export
- **Solution**: Remove getServerSideProps, ISR, or API routes

### Debug Steps

1. **Check browser console** for JavaScript errors
2. **Verify .htaccess** is working (check network tab)
3. **Test API endpoints** individually
4. **Check file permissions** (755 for directories, 644 for files)
5. **Review error logs** in cPanel

## 📊 Performance Optimization

### 1. Enable Compression

The .htaccess file includes gzip compression. Verify it's working:
- Use browser dev tools
- Check response headers for `Content-Encoding: gzip`

### 2. Cache Static Assets

Static assets are cached for 1 year. Monitor:
- Cache hit rates
- Load times
- Core Web Vitals

### 3. Optimize Images

- Use WebP format when possible
- Compress images before upload
- Consider using a CDN for large images

## 🔒 Security Considerations

### 1. Environment Variables

- Never commit `.env.production` to version control
- Use strong, unique passwords
- Rotate API keys regularly

### 2. File Permissions

Set correct permissions:
```bash
# Directories
chmod 755 public_html/

# Files
chmod 644 public_html/*.html
chmod 644 public_html/.htaccess
```

### 3. Security Headers

The .htaccess includes security headers:
- X-Content-Type-Options
- X-Frame-Options
- X-XSS-Protection
- Referrer-Policy

## 📈 Monitoring

### 1. Error Tracking

Monitor for:
- 404 errors (routing issues)
- 500 errors (server configuration)
- JavaScript errors (client-side issues)

### 2. Performance Metrics

Track:
- Page load times
- Time to First Byte (TTFB)
- Core Web Vitals
- API response times

## 🆘 Support

If you encounter issues:

1. **Run diagnostics**: `npm run cpanel:check`
2. **Check logs**: Review cPanel error logs
3. **Test locally**: Ensure `npm run build:cpanel` works
4. **Contact host**: Some issues may be hosting-specific

## 📚 Additional Resources

- [Next.js Static Export Documentation](https://nextjs.org/docs/app/building-your-application/deploying/static-exports)
- [cPanel Documentation](https://docs.cpanel.net/)
- [Apache .htaccess Guide](https://httpd.apache.org/docs/current/howto/htaccess.html)

## 🚀 Quick Reference Commands

```bash
# Setup project for cPanel
npm run cpanel:setup

# Check compatibility
npm run cpanel:check

# Build for cPanel
npm run build:cpanel

# Generate deployment files
npm run deploy:cpanel

# Install dependencies (if needed)
npm install cross-env --save-dev
```

## 📋 Deployment Checklist

- [ ] Run `npm run cpanel:setup`
- [ ] Configure `.env.production` with your values
- [ ] Run `npm run cpanel:check` to verify setup
- [ ] Create MySQL database in cPanel
- [ ] Update database credentials in `.env.production`
- [ ] Run `npm run deploy:cpanel`
- [ ] Upload `out` directory contents to `public_html`
- [ ] Verify `.htaccess` is uploaded
- [ ] Test your website
- [ ] Set up SSL certificate (recommended)

---

**Need help?** Check the troubleshooting section or run `npm run cpanel:check` for automated diagnostics.
