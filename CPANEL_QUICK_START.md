# 🚀 cPanel Quick Start Guide

Deploy your Canva Clone to cPanel hosting in 5 simple steps!

## ⚡ Quick Deploy (5 minutes)

### 1. Setup
```bash
npm run cpanel:setup
```

### 2. Configure
Edit `.env.production` with your domain and API keys:
```bash
NEXT_PUBLIC_APP_URL=https://yourdomain.com
CPANEL_DEPLOY=true
# Add your database and API credentials
```

### 3. Check
```bash
npm run cpanel:check
```

### 4. Build
```bash
npm run deploy:cpanel
```

### 5. Upload
Upload all files from the `out` directory to your cPanel `public_html` folder.

## 🗄️ Database Setup

1. **Create MySQL database** in cPanel
2. **Update `.env.production`** with database credentials
3. **Import your schema** (if needed)

## ✅ Verification

- [ ] Website loads at your domain
- [ ] All routes work (no 404s)
- [ ] Images display correctly
- [ ] API functions work
- [ ] Database connections successful

## 🆘 Need Help?

- **Issues?** Run `npm run cpanel:check`
- **Detailed guide:** See `CPANEL_DEPLOYMENT.md`
- **Troubleshooting:** Check browser console and cPanel error logs

## 📋 Common Commands

```bash
# Full deployment process
npm run cpanel:setup
npm run cpanel:check
npm run deploy:cpanel

# Development
npm run dev

# Testing build
npm run build:cpanel
```

---

**🎉 That's it!** Your Canva Clone should now be running on cPanel hosting.
