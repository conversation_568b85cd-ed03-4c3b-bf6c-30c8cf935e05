/**
 * cPanel Deployment Configuration
 * This file contains configuration for deploying the Next.js app to cPanel
 */

const path = require('path');
const fs = require('fs');

const cPanelConfig = {
  // Build configuration for cPanel
  build: {
    // Output directory for static export
    outDir: 'out',
    
    // Whether to use static export (required for most cPanel hosting)
    staticExport: true,
    
    // Whether to generate a sitemap
    generateSitemap: true,
    
    // Base path if deploying to a subdirectory
    basePath: process.env.CPANEL_BASE_PATH || '',
    
    // Asset prefix for CDN or subdirectory deployment
    assetPrefix: process.env.CPANEL_ASSET_PREFIX || '',
  },
  
  // Database configuration for cPanel
  database: {
    // Most cPanel hosting uses MySQL instead of PostgreSQL
    type: 'mysql',
    
    // Connection settings (to be set in environment variables)
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    database: process.env.DB_NAME,
    username: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    
    // SSL configuration for cPanel databases
    ssl: {
      rejectUnauthorized: false
    }
  },
  
  // File upload configuration
  uploads: {
    // Directory for uploaded files (relative to public folder)
    directory: 'uploads',
    
    // Maximum file size (in bytes)
    maxFileSize: 10 * 1024 * 1024, // 10MB
    
    // Allowed file types
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
  },
  
  // API configuration
  api: {
    // Whether to use external API endpoints (for static export)
    useExternal: true,
    
    // Base URL for API calls
    baseUrl: process.env.NEXT_PUBLIC_APP_URL || 'https://yourdomain.com',
    
    // Timeout for API calls
    timeout: 30000
  },
  
  // Security configuration
  security: {
    // CORS origins
    corsOrigins: [
      process.env.NEXT_PUBLIC_APP_URL,
      'https://yourdomain.com'
    ],
    
    // Rate limiting
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100 // limit each IP to 100 requests per windowMs
    }
  }
};

module.exports = cPanelConfig;
