 Warning: Extra attributes from the server: webcrx 
overrideMethod @ installHook.js:1
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 Template loaded: Object
 Setting initial customizations: Object
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 Template loaded: Object
 Setting initial customizations: Object
 [Fast Refresh] rebuilding
 🔄 Fixed localhost port mismatch: Object
 🔄 Fixed localhost port mismatch: Object
 🔄 Fixed localhost port mismatch: Object
 🔒 PUBLIC MODE: Creating isolated template copy to prevent original template modification
 🔄 Fixed localhost port mismatch: Object
 🔄 Fixed localhost port mismatch: Object
 🔄 Fixed localhost port mismatch: Object
 🔒 PUBLIC MODE: Creating isolated template copy to prevent original template modification
 🎨 CustomizationEditor mounted with templateData: Object
 🔒 PUBLIC CUSTOMIZATION MODE ACTIVE - All changes are temporary and will NOT affect the original template
 📋 Template JSON structure: Object
 🖼️ Image objects found: 3
 🖼️ Image 1: Object
 🖼️ Image 2: Object
 🖼️ Image 3: Object
 Canvas init attempt 1/10: Object
 Canvas initialized with dimensions: 1012 x 506
 Setting up event listeners for download and preview
 🚫 No editor canvas available for selection
 Cleaning up event listeners
 🎨 CustomizationEditor mounted with templateData: Object
 🔒 PUBLIC CUSTOMIZATION MODE ACTIVE - All changes are temporary and will NOT affect the original template
 📋 Template JSON structure: Object
 🖼️ Image objects found: 3
 🖼️ Image 1: Object
 🖼️ Image 2: Object
 🖼️ Image 3: Object
 Canvas init attempt 1/10: Object
 Canvas initialized with dimensions: 1012 x 506
 Setting up event listeners for download and preview
 🚫 No editor canvas available for selection
 Cleaning up event listeners
 useLoadState: Attempting to load initial state
 useLoadState: Parsed JSON data: Object
 useLoadState: Canvas dimensions: Object
 useLoadState: Loading JSON into canvas...
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
 🎨 Editor canvas initialized: Object
 Editor ready in public page: Object
 🔄 Resetting initial viewport transform to identity matrix
 Editor is ready, checking for content and centering workspace
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(1)
 ⏳ Canvas still loading, deferring selection
 💾 PUBLIC: Save callback triggered, generating preview
 💾 PUBLIC: Save callback triggered, generating preview
 💾 PUBLIC: Save callback triggered, generating preview
 💾 PUBLIC: Save callback triggered, generating preview
 💾 PUBLIC: Save callback triggered, generating preview
 💾 PUBLIC: Save callback triggered, generating preview
 💾 PUBLIC: Save callback triggered, generating preview
 💾 PUBLIC: Save callback triggered, generating preview
 💾 PUBLIC: Save callback triggered, generating preview
 useLoadState: JSON loaded successfully, objects count: 8
 useLoadState: Template loading complete
 Cleaning up event listeners
 🎨 Editor canvas initialized: Object
 🖼️ Canvas Image 2: Object
 🖼️ Canvas Image 3: Object
 🖼️ Canvas Image 4: Object
 Editor ready in public page: Object
 🔄 Resetting initial viewport transform to identity matrix
 Editor is ready, checking for content and centering workspace
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(1)
 ⏳ Canvas still loading, deferring selection
 Cleaning up event listeners
 🎨 Editor canvas initialized: Object
 🖼️ Canvas Image 2: Object
 🖼️ Canvas Image 3: Object
 🖼️ Canvas Image 4: Object
 Editor ready in public page: Object
 🔄 Resetting initial viewport transform to identity matrix
 Editor is ready, checking for content and centering workspace
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(1)
 ⏳ Canvas still loading, deferring selection
 Applying customizations: Object
 Template editable layers: Array(1)
 Processing layer layer_1753820023942 (image): undefined
 Layer original value: {"left":1233.9679865976686,"top":369.2814917948255,"width":742,"height":801,"scaleX":0.9703504043126685,"scaleY":0.9703504043126685,"angle":0,"src":"http://localhost:3000/api/upload?file=1753819967507-yaqjxrz3z3c.png","uniqueId":"1234_369_720_777"}
 Custom value type: undefined Original value type: string
 Values equal? false
 Skipping layer layer_1753820023942 - no custom value or same as original
 Canvas has 8 objects (3 images), checking for content...
 Canvas has content and all images loaded, centering workspace
 Applying customizations: Object
 Template editable layers: Array(1)
 Processing layer layer_1753820023942 (image): undefined
 Layer original value: {"left":1233.9679865976686,"top":369.2814917948255,"width":742,"height":801,"scaleX":0.9703504043126685,"scaleY":0.9703504043126685,"angle":0,"src":"http://localhost:3000/api/upload?file=1753819967507-yaqjxrz3z3c.png","uniqueId":"1234_369_720_777"}
 Custom value type: undefined Original value type: string
 Values equal? false
 Skipping layer layer_1753820023942 - no custom value or same as original
 Applying customizations: Object
 Template editable layers: Array(1)
 Processing layer layer_1753820023942 (image): undefined
 Layer original value: {"left":1233.9679865976686,"top":369.2814917948255,"width":742,"height":801,"scaleX":0.9703504043126685,"scaleY":0.9703504043126685,"angle":0,"src":"http://localhost:3000/api/upload?file=1753819967507-yaqjxrz3z3c.png","uniqueId":"1234_369_720_777"}
 Custom value type: undefined Original value type: string
 Values equal? false
 Skipping layer layer_1753820023942 - no custom value or same as original
 Cleaning up event listeners
 🎨 Editor canvas initialized: Object
 🖼️ Canvas Image 2: Object
 🖼️ Canvas Image 3: Object
 🖼️ Canvas Image 4: Object
 Editor ready in public page: Object
 🔄 Resetting initial viewport transform to identity matrix
 Editor is ready, checking for content and centering workspace
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(1)
 🔥 External active layer changed: null
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 8
 Clearing canvas selection
 Trying to match image layer layer_1753820023942 (Image 1) with 3 available image objects
 Available image objects: Array(3)
 Using stored properties for matching layer layer_1753820023942: Object
 Matched image by exact position/size for layer layer_1753820023942
 ✅ Assigning ID layer_1753820023942 to image object: Object
 Locking non-editable objects...
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753820023942)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: textbox (ID: no-id)
 🔒 Locked non-editable object: textbox (ID: no-id)
 Canvas has 8 objects (3 images), checking for content...
 Canvas has content and all images loaded, centering workspace
 Canvas has 8 objects (3 images), checking for content...
 Canvas has content and all images loaded, centering workspace
 ⚠️ Object already has ID: layer_1753820023942 for layer layer_1753820023942
 Locking non-editable objects...
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753820023942)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: textbox (ID: no-id)
 🔒 Locked non-editable object: textbox (ID: no-id)
 ⚠️ Object already has ID: layer_1753820023942 for layer layer_1753820023942
 Locking non-editable objects...
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753820023942)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: textbox (ID: no-id)
 🔒 Locked non-editable object: textbox (ID: no-id)
 🔄 Force rendering canvas...
 📊 Image loading status: 3/3 images loaded
 🔄 Force rendering canvas...
 📊 Image loading status: 3/3 images loaded
 🔄 Force rendering canvas...
 📊 Image loading status: 3/3 images loaded
 Applying customizations: Object
 Template editable layers: Array(1)
 Processing layer layer_1753820023942 (image): undefined
 Layer original value: {"left":1233.9679865976686,"top":369.2814917948255,"width":742,"height":801,"scaleX":0.9703504043126685,"scaleY":0.9703504043126685,"angle":0,"src":"http://localhost:3000/api/upload?file=1753819967507-yaqjxrz3z3c.png","uniqueId":"1234_369_720_777"}
 Custom value type: undefined Original value type: string
 Values equal? false
 Skipping layer layer_1753820023942 - no custom value or same as original
 Canvas has 8 objects (3 images), checking for content...
 Canvas has content and all images loaded, centering workspace
 Template loaded successfully, clearing loading state
 Template loaded successfully, clearing loading state
 Template loaded successfully, clearing loading state
 🔍 centerWorkspace: Found objects: 8 Array(8)
 🔄 centerWorkspace: No workspace object found, centering all content
 📐 centerWorkspace: Content bounds: Object
 📐 centerWorkspace: Container dimensions: Object
 ✅ centerWorkspace: Applying content-based transform: Object
 🎯 Content centered and fitted successfully
 ⚠️ Object already has ID: layer_1753820023942 for layer layer_1753820023942
 Locking non-editable objects...
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753820023942)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: textbox (ID: no-id)
 🔒 Locked non-editable object: textbox (ID: no-id)
 ✅ centerWorkspace: Already properly centered, skipping...
 ✅ centerWorkspace: Already properly centered, skipping...
 🔄 Force rendering canvas...
 📊 Image loading status: 3/3 images loaded
 Template loaded successfully, clearing loading state
 ✅ centerWorkspace: Already properly centered, skipping...
 🔒 Re-locked object: rect (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753820023942)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: rect (ID: undefined)
 🔒 Re-locked object: rect (ID: undefined)
 🔒 Re-locked object: textbox (ID: undefined)
 🔒 Re-locked object: textbox (ID: undefined)
 🔒 Re-locked object: rect (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753820023942)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: rect (ID: undefined)
 🔒 Re-locked object: rect (ID: undefined)
 🔒 Re-locked object: textbox (ID: undefined)
 🔒 Re-locked object: textbox (ID: undefined)
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 🔒 Re-locked object: rect (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753820023942)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: rect (ID: undefined)
 🔒 Re-locked object: rect (ID: undefined)
 🔒 Re-locked object: textbox (ID: undefined)
 🔒 Re-locked object: textbox (ID: undefined)
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 🔒 Re-locked object: rect (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753820023942)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: rect (ID: undefined)
 🔒 Re-locked object: rect (ID: undefined)
 🔒 Re-locked object: textbox (ID: undefined)
 🔒 Re-locked object: textbox (ID: undefined)
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 🔒 Re-locked object: rect (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753820023942)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: rect (ID: undefined)
 🔒 Re-locked object: rect (ID: undefined)
 🔒 Re-locked object: textbox (ID: undefined)
 🔒 Re-locked object: textbox (ID: undefined)
 🔒 Re-locked object: rect (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753820023942)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: rect (ID: undefined)
 🔒 Re-locked object: rect (ID: undefined)
 🔒 Re-locked object: textbox (ID: undefined)
 🔒 Re-locked object: textbox (ID: undefined)
 Cleaning up event listeners
 🎨 Editor canvas initialized: Object
 🖼️ Canvas Image 2: Object
 🖼️ Canvas Image 3: Object
 🖼️ Canvas Image 4: Object
 Editor ready in public page: Object
 🔄 Resetting initial viewport transform to identity matrix
 Editor is ready, checking for content and centering workspace
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(1)
 🔥 External active layer changed: null
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 8
 Clearing canvas selection
 🎯 handleLayerActivation called with: null
 🎯 Previous activeLayerId: null
 🎯 Setting activeLayerId to: null
 Canvas selection cleared
 🎯 handleLayerActivation called with: null
 🎯 Previous activeLayerId: null
 🎯 Setting activeLayerId to: null
 Cleaning up event listeners
 🎨 Editor canvas initialized: Object
 🖼️ Canvas Image 2: Object
 🖼️ Canvas Image 3: Object
 🖼️ Canvas Image 4: Object
 Editor ready in public page: Object
 🔄 Resetting initial viewport transform to identity matrix
 Editor is ready, checking for content and centering workspace
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(1)
 🔥 External active layer changed: null
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 8
 Clearing canvas selection
 Applying customizations: Object
 Template editable layers: Array(1)
 Processing layer layer_1753820023942 (image): undefined
 Layer original value: {"left":1233.9679865976686,"top":369.2814917948255,"width":742,"height":801,"scaleX":0.9703504043126685,"scaleY":0.9703504043126685,"angle":0,"src":"http://localhost:3000/api/upload?file=1753819967507-yaqjxrz3z3c.png","uniqueId":"1234_369_720_777"}
 Custom value type: undefined Original value type: string
 Values equal? false
 Skipping layer layer_1753820023942 - no custom value or same as original
 Applying customizations: Object
 Template editable layers: Array(1)
 Processing layer layer_1753820023942 (image): undefined
 Layer original value: {"left":1233.9679865976686,"top":369.2814917948255,"width":742,"height":801,"scaleX":0.9703504043126685,"scaleY":0.9703504043126685,"angle":0,"src":"http://localhost:3000/api/upload?file=1753819967507-yaqjxrz3z3c.png","uniqueId":"1234_369_720_777"}
 Custom value type: undefined Original value type: string
 Values equal? false
 Skipping layer layer_1753820023942 - no custom value or same as original
 Canvas has 8 objects (3 images), checking for content...
 Canvas has content and all images loaded, centering workspace
 Canvas has 8 objects (3 images), checking for content...
 Canvas has content and all images loaded, centering workspace
 ⚠️ Object already has ID: layer_1753820023942 for layer layer_1753820023942
 Locking non-editable objects...
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753820023942)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: textbox (ID: no-id)
 🔒 Locked non-editable object: textbox (ID: no-id)
 ⚠️ Object already has ID: layer_1753820023942 for layer layer_1753820023942
 Locking non-editable objects...
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753820023942)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: textbox (ID: no-id)
 🔒 Locked non-editable object: textbox (ID: no-id)
 🔍 centerWorkspace: Found objects: 8 Array(8)
 🔄 centerWorkspace: No workspace object found, centering all content
 📐 centerWorkspace: Content bounds: Object
 📐 centerWorkspace: Container dimensions: Object
 ✅ centerWorkspace: Applying content-based transform: Object
 🎯 Content centered and fitted successfully
 Template loaded successfully, clearing loading state
 ✅ centerWorkspace: Already properly centered, skipping...
 Template loaded successfully, clearing loading state
 🔄 Force rendering canvas...
 📊 Image loading status: 3/3 images loaded
 🔄 Force rendering canvas...
 📊 Image loading status: 3/3 images loaded
 🔒 Re-locked object: rect (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753820023942)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: rect (ID: undefined)
 🔒 Re-locked object: rect (ID: undefined)
 🔒 Re-locked object: textbox (ID: undefined)
 🔒 Re-locked object: textbox (ID: undefined)
 Cleaning up event listeners
 🎨 Editor canvas initialized: Object
 🖼️ Canvas Image 2: Object
 🖼️ Canvas Image 3: Object
 🖼️ Canvas Image 4: Object
 Editor ready in public page: Object
 🔄 Resetting initial viewport transform to identity matrix
 Editor is ready, checking for content and centering workspace
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(1)
 🔥 External active layer changed: null
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 8
 Clearing canvas selection
 🎯 handleLayerActivation called with: null
 🎯 Previous activeLayerId: null
 🎯 Setting activeLayerId to: null
 Canvas selection cleared
 🎯 handleLayerActivation called with: null
 🎯 Previous activeLayerId: null
 🎯 Setting activeLayerId to: null
 Cleaning up event listeners
 🎨 Editor canvas initialized: Object
 🖼️ Canvas Image 2: Object
 🖼️ Canvas Image 3: Object
 🖼️ Canvas Image 4: Object
 Editor ready in public page: Object
 🔄 Resetting initial viewport transform to identity matrix
 Editor is ready, checking for content and centering workspace
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(1)
 🔥 External active layer changed: null
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 8
 Clearing canvas selection
 Applying customizations: Object
 Template editable layers: Array(1)
 Processing layer layer_1753820023942 (image): undefined
 Layer original value: {"left":1233.9679865976686,"top":369.2814917948255,"width":742,"height":801,"scaleX":0.9703504043126685,"scaleY":0.9703504043126685,"angle":0,"src":"http://localhost:3000/api/upload?file=1753819967507-yaqjxrz3z3c.png","uniqueId":"1234_369_720_777"}
 Custom value type: undefined Original value type: string
 Values equal? false
 Skipping layer layer_1753820023942 - no custom value or same as original
 Applying customizations: Object
 Template editable layers: Array(1)
 Processing layer layer_1753820023942 (image): undefined
 Layer original value: {"left":1233.9679865976686,"top":369.2814917948255,"width":742,"height":801,"scaleX":0.9703504043126685,"scaleY":0.9703504043126685,"angle":0,"src":"http://localhost:3000/api/upload?file=1753819967507-yaqjxrz3z3c.png","uniqueId":"1234_369_720_777"}
 Custom value type: undefined Original value type: string
 Values equal? false
 Skipping layer layer_1753820023942 - no custom value or same as original
 Canvas has 8 objects (3 images), checking for content...
 Canvas has content and all images loaded, centering workspace
 Canvas has 8 objects (3 images), checking for content...
 Canvas has content and all images loaded, centering workspace
 ⚠️ Object already has ID: layer_1753820023942 for layer layer_1753820023942
 Locking non-editable objects...
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753820023942)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: textbox (ID: no-id)
 🔒 Locked non-editable object: textbox (ID: no-id)
 ⚠️ Object already has ID: layer_1753820023942 for layer layer_1753820023942
 Locking non-editable objects...
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753820023942)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: textbox (ID: no-id)
 🔒 Locked non-editable object: textbox (ID: no-id)
 🔍 centerWorkspace: Found objects: 8 Array(8)
 🔄 centerWorkspace: No workspace object found, centering all content
 📐 centerWorkspace: Content bounds: Object
 📐 centerWorkspace: Container dimensions: Object
 ✅ centerWorkspace: Applying content-based transform: Object
 🎯 Content centered and fitted successfully
 Template loaded successfully, clearing loading state
 ✅ centerWorkspace: Already properly centered, skipping...
 Template loaded successfully, clearing loading state
 🔄 Force rendering canvas...
 📊 Image loading status: 3/3 images loaded
 🔄 Force rendering canvas...
 📊 Image loading status: 3/3 images loaded
 Cleaning up event listeners
 🎨 Editor canvas initialized: Object
 🖼️ Canvas Image 2: Object
 🖼️ Canvas Image 3: Object
 🖼️ Canvas Image 4: Object
 Editor ready in public page: Object
 🔄 Resetting initial viewport transform to identity matrix
 Editor is ready, checking for content and centering workspace
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(1)
 🔥 External active layer changed: null
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 8
 Clearing canvas selection
 🎯 handleLayerActivation called with: null
 🎯 Previous activeLayerId: null
 🎯 Setting activeLayerId to: null
 Canvas selection cleared
 🎯 handleLayerActivation called with: null
 🎯 Previous activeLayerId: null
 🎯 Setting activeLayerId to: null
 Cleaning up event listeners
 🎨 Editor canvas initialized: Object
 🖼️ Canvas Image 2: Object
 🖼️ Canvas Image 3: Object
 🖼️ Canvas Image 4: Object
 Editor ready in public page: Object
 🔄 Resetting initial viewport transform to identity matrix
 Editor is ready, checking for content and centering workspace
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(1)
 🔥 External active layer changed: null
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 8
 Clearing canvas selection
 Applying customizations: Object
 Template editable layers: Array(1)
 Processing layer layer_1753820023942 (image): undefined
 Layer original value: {"left":1233.9679865976686,"top":369.2814917948255,"width":742,"height":801,"scaleX":0.9703504043126685,"scaleY":0.9703504043126685,"angle":0,"src":"http://localhost:3000/api/upload?file=1753819967507-yaqjxrz3z3c.png","uniqueId":"1234_369_720_777"}
 Custom value type: undefined Original value type: string
 Values equal? false
 Skipping layer layer_1753820023942 - no custom value or same as original
 Applying customizations: Object
 Template editable layers: Array(1)
 Processing layer layer_1753820023942 (image): undefined
 Layer original value: {"left":1233.9679865976686,"top":369.2814917948255,"width":742,"height":801,"scaleX":0.9703504043126685,"scaleY":0.9703504043126685,"angle":0,"src":"http://localhost:3000/api/upload?file=1753819967507-yaqjxrz3z3c.png","uniqueId":"1234_369_720_777"}
 Custom value type: undefined Original value type: string
 Values equal? false
 Skipping layer layer_1753820023942 - no custom value or same as original
 Canvas has 8 objects (3 images), checking for content...
 Canvas has content and all images loaded, centering workspace
 Canvas has 8 objects (3 images), checking for content...
 Canvas has content and all images loaded, centering workspace
 ⚠️ Object already has ID: layer_1753820023942 for layer layer_1753820023942
 Locking non-editable objects...
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753820023942)
 🔒 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:2657 🔒 Locked non-editable object: rect (ID: no-id)
customization-editor.tsx:2657 🔒 Locked non-editable object: rect (ID: no-id)
customization-editor.tsx:2657 🔒 Locked non-editable object: textbox (ID: no-id)
customization-editor.tsx:2657 🔒 Locked non-editable object: textbox (ID: no-id)
customization-editor.tsx:2630 ⚠️ Object already has ID: layer_1753820023942 for layer layer_1753820023942
customization-editor.tsx:2637 Locking non-editable objects...
customization-editor.tsx:2657 🔒 Locked non-editable object: rect (ID: no-id)
customization-editor.tsx:2657 🔒 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:2665 🔓 Enabled interaction for editable object: image (ID: layer_1753820023942)
customization-editor.tsx:2657 🔒 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:2657 🔒 Locked non-editable object: rect (ID: no-id)
customization-editor.tsx:2657 🔒 Locked non-editable object: rect (ID: no-id)
customization-editor.tsx:2657 🔒 Locked non-editable object: textbox (ID: no-id)
customization-editor.tsx:2657 🔒 Locked non-editable object: textbox (ID: no-id)
customization-editor.tsx:1473 🔍 centerWorkspace: Found objects: 8 Array(8)
customization-editor.tsx:1521 🔄 centerWorkspace: No workspace object found, centering all content
customization-editor.tsx:1545 📐 centerWorkspace: Content bounds: Object
customization-editor.tsx:1554 📐 centerWorkspace: Container dimensions: Object
customization-editor.tsx:1576 ✅ centerWorkspace: Applying content-based transform: Object
customization-editor.tsx:1587 🎯 Content centered and fitted successfully
customization-editor.tsx:2676 Template loaded successfully, clearing loading state
customization-editor.tsx:1465 ✅ centerWorkspace: Already properly centered, skipping...
customization-editor.tsx:2676 Template loaded successfully, clearing loading state
customization-editor.tsx:602 🔄 Force rendering canvas...
customization-editor.tsx:620 📊 Image loading status: 3/3 images loaded
customization-editor.tsx:602 🔄 Force rendering canvas...
customization-editor.tsx:620 📊 Image loading status: 3/3 images loaded
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:690 🔓 Re-enabled interaction for object: image (ID: layer_1753820023942)
customization-editor.tsx:674 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:690 🔓 Re-enabled interaction for object: image (ID: layer_1753820023942)
customization-editor.tsx:674 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:690 🔓 Re-enabled interaction for object: image (ID: layer_1753820023942)
customization-editor.tsx:674 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: textbox (ID: undefined)
hot-reloader-client.tsx:297 [Fast Refresh] rebuilding
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:690 🔓 Re-enabled interaction for object: image (ID: layer_1753820023942)
customization-editor.tsx:674 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: textbox (ID: undefined)
hot-reloader-client.tsx:297 [Fast Refresh] rebuilding
hot-reloader-client.tsx:297 [Fast Refresh] rebuilding
hot-reloader-client.tsx:297 [Fast Refresh] rebuilding
hot-reloader-client.tsx:297 [Fast Refresh] rebuilding
hot-reloader-client.tsx:297 [Fast Refresh] rebuilding
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:690 🔓 Re-enabled interaction for object: image (ID: layer_1753820023942)
customization-editor.tsx:674 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:690 🔓 Re-enabled interaction for object: image (ID: layer_1753820023942)
customization-editor.tsx:674 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:690 🔓 Re-enabled interaction for object: image (ID: layer_1753820023942)
customization-editor.tsx:674 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:690 🔓 Re-enabled interaction for object: image (ID: layer_1753820023942)
customization-editor.tsx:674 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:2466 Cleaning up event listeners
customization-editor.tsx:575 🎨 Editor canvas initialized: {canvasWidth: 933, canvasHeight: 506, objectCount: 8, hasContext: true}
customization-editor.tsx:587 🖼️ Canvas Image 2: {type: 'image', id: undefined, src: 'http://localhost:3001/api/upload?file=1753819456492-5eyhlcm6hpy.jpg', width: 990, height: 1280, …}
customization-editor.tsx:587 🖼️ Canvas Image 3: {type: 'image', id: 'layer_1753820023942', src: 'http://localhost:3001/api/upload?file=1753819967507-yaqjxrz3z3c.png', width: 742, height: 801, …}
customization-editor.tsx:587 🖼️ Canvas Image 4: {type: 'image', id: undefined, src: 'http://localhost:3001/api/upload?file=1753819489388-lznjezpv5uk.jpeg', width: 186, height: 272, …}
page.tsx:321 Editor ready in public page: {savePng: ƒ, saveJpg: ƒ, saveSvg: ƒ, saveJson: ƒ, loadJson: ƒ, …}
customization-editor.tsx:1603 🔄 Resetting initial viewport transform to identity matrix
customization-editor.tsx:1607 Editor is ready, checking for content and centering workspace
customization-editor.tsx:2460 Setting up event listeners for download and preview
customization-editor.tsx:2480 Setting up canvas object IDs for editable layers: ['layer_1753820023942']
customization-editor.tsx:2697 🔥 External active layer changed: null
customization-editor.tsx:2698 🔥 Editor canvas available: true
customization-editor.tsx:2699 🔥 Canvas objects count: 8
customization-editor.tsx:2754 Clearing canvas selection
page.tsx:240 🎯 handleLayerActivation called with: null
page.tsx:241 🎯 Previous activeLayerId: null
page.tsx:243 🎯 Setting activeLayerId to: null
customization-editor.tsx:1759 Canvas selection cleared
page.tsx:240 🎯 handleLayerActivation called with: null
page.tsx:241 🎯 Previous activeLayerId: null
page.tsx:243 🎯 Setting activeLayerId to: null
customization-editor.tsx:2466 Cleaning up event listeners
customization-editor.tsx:575 🎨 Editor canvas initialized: {canvasWidth: 933, canvasHeight: 506, objectCount: 8, hasContext: true}
customization-editor.tsx:587 🖼️ Canvas Image 2: {type: 'image', id: undefined, src: 'http://localhost:3001/api/upload?file=1753819456492-5eyhlcm6hpy.jpg', width: 990, height: 1280, …}
customization-editor.tsx:587 🖼️ Canvas Image 3: {type: 'image', id: 'layer_1753820023942', src: 'http://localhost:3001/api/upload?file=1753819967507-yaqjxrz3z3c.png', width: 742, height: 801, …}
customization-editor.tsx:587 🖼️ Canvas Image 4: {type: 'image', id: undefined, src: 'http://localhost:3001/api/upload?file=1753819489388-lznjezpv5uk.jpeg', width: 186, height: 272, …}
page.tsx:321 Editor ready in public page: {savePng: ƒ, saveJpg: ƒ, saveSvg: ƒ, saveJson: ƒ, loadJson: ƒ, …}
customization-editor.tsx:1603 🔄 Resetting initial viewport transform to identity matrix
customization-editor.tsx:1607 Editor is ready, checking for content and centering workspace
customization-editor.tsx:2460 Setting up event listeners for download and preview
customization-editor.tsx:2480 Setting up canvas object IDs for editable layers: ['layer_1753820023942']
customization-editor.tsx:2697 🔥 External active layer changed: null
customization-editor.tsx:2698 🔥 Editor canvas available: true
customization-editor.tsx:2699 🔥 Canvas objects count: 8
customization-editor.tsx:2754 Clearing canvas selection
customization-editor.tsx:1882 Applying customizations: {}
customization-editor.tsx:1883 Template editable layers: [{…}]
customization-editor.tsx:1887 Processing layer layer_1753820023942 (image): undefined
customization-editor.tsx:1888 Layer original value: {"left":1233.9679865976686,"top":369.2814917948255,"width":742,"height":801,"scaleX":0.9703504043126685,"scaleY":0.9703504043126685,"angle":0,"src":"http://localhost:3000/api/upload?file=1753819967507-yaqjxrz3z3c.png","uniqueId":"1234_369_720_777"}
customization-editor.tsx:1889 Custom value type: undefined Original value type: string
customization-editor.tsx:1890 Values equal? false
customization-editor.tsx:1894 Skipping layer layer_1753820023942 - no custom value or same as original
customization-editor.tsx:1882 Applying customizations: {}
customization-editor.tsx:1883 Template editable layers: [{…}]
customization-editor.tsx:1887 Processing layer layer_1753820023942 (image): undefined
customization-editor.tsx:1888 Layer original value: {"left":1233.9679865976686,"top":369.2814917948255,"width":742,"height":801,"scaleX":0.9703504043126685,"scaleY":0.9703504043126685,"angle":0,"src":"http://localhost:3000/api/upload?file=1753819967507-yaqjxrz3z3c.png","uniqueId":"1234_369_720_777"}
customization-editor.tsx:1889 Custom value type: undefined Original value type: string
customization-editor.tsx:1890 Values equal? false
customization-editor.tsx:1894 Skipping layer layer_1753820023942 - no custom value or same as original
customization-editor.tsx:1614 Canvas has 8 objects (3 images), checking for content...
customization-editor.tsx:1624 Canvas has content and all images loaded, centering workspace
customization-editor.tsx:1614 Canvas has 8 objects (3 images), checking for content...
customization-editor.tsx:1624 Canvas has content and all images loaded, centering workspace
customization-editor.tsx:2630 ⚠️ Object already has ID: layer_1753820023942 for layer layer_1753820023942
customization-editor.tsx:2637 Locking non-editable objects...
customization-editor.tsx:2657 🔒 Locked non-editable object: rect (ID: no-id)
customization-editor.tsx:2657 🔒 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:2665 🔓 Enabled interaction for editable object: image (ID: layer_1753820023942)
customization-editor.tsx:2657 🔒 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:2657 🔒 Locked non-editable object: rect (ID: no-id)
customization-editor.tsx:2657 🔒 Locked non-editable object: rect (ID: no-id)
customization-editor.tsx:2657 🔒 Locked non-editable object: textbox (ID: no-id)
customization-editor.tsx:2657 🔒 Locked non-editable object: textbox (ID: no-id)
customization-editor.tsx:2630 ⚠️ Object already has ID: layer_1753820023942 for layer layer_1753820023942
customization-editor.tsx:2637 Locking non-editable objects...
customization-editor.tsx:2657 🔒 Locked non-editable object: rect (ID: no-id)
customization-editor.tsx:2657 🔒 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:2665 🔓 Enabled interaction for editable object: image (ID: layer_1753820023942)
customization-editor.tsx:2657 🔒 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:2657 🔒 Locked non-editable object: rect (ID: no-id)
customization-editor.tsx:2657 🔒 Locked non-editable object: rect (ID: no-id)
customization-editor.tsx:2657 🔒 Locked non-editable object: textbox (ID: no-id)
customization-editor.tsx:2657 🔒 Locked non-editable object: textbox (ID: no-id)
customization-editor.tsx:1473 🔍 centerWorkspace: Found objects: 8 (8) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
customization-editor.tsx:1521 🔄 centerWorkspace: No workspace object found, centering all content
customization-editor.tsx:1545 📐 centerWorkspace: Content bounds: {minX: 706.81, minY: -99.52999999999992, maxX: 1696.69, maxY: 1124, contentWidth: 989.8800000000001, …}
customization-editor.tsx:1554 📐 centerWorkspace: Container dimensions: {containerWidth: 933, containerHeight: 506}
customization-editor.tsx:1576 ✅ centerWorkspace: Applying content-based transform: {scale: 0.38086520150711467, offsetX: 8.795244088824973, offsetY: 57.907513506003056, containerCenter: {…}, contentCenter: {…}, …}
customization-editor.tsx:1587 🎯 Content centered and fitted successfully
customization-editor.tsx:2676 Template loaded successfully, clearing loading state
customization-editor.tsx:1465 ✅ centerWorkspace: Already properly centered, skipping...
customization-editor.tsx:2676 Template loaded successfully, clearing loading state
customization-editor.tsx:602 🔄 Force rendering canvas...
customization-editor.tsx:620 📊 Image loading status: 3/3 images loaded
customization-editor.tsx:602 🔄 Force rendering canvas...
customization-editor.tsx:620 📊 Image loading status: 3/3 images loaded
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:690 🔓 Re-enabled interaction for object: image (ID: layer_1753820023942)
customization-editor.tsx:674 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:690 🔓 Re-enabled interaction for object: image (ID: layer_1753820023942)
customization-editor.tsx:674 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:690 🔓 Re-enabled interaction for object: image (ID: layer_1753820023942)
customization-editor.tsx:674 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:2466 Cleaning up event listeners
customization-editor.tsx:575 🎨 Editor canvas initialized: {canvasWidth: 933, canvasHeight: 506, objectCount: 8, hasContext: true}
customization-editor.tsx:587 🖼️ Canvas Image 2: {type: 'image', id: undefined, src: 'http://localhost:3001/api/upload?file=1753819456492-5eyhlcm6hpy.jpg', width: 990, height: 1280, …}
customization-editor.tsx:587 🖼️ Canvas Image 3: {type: 'image', id: 'layer_1753820023942', src: 'http://localhost:3001/api/upload?file=1753819967507-yaqjxrz3z3c.png', width: 742, height: 801, …}
customization-editor.tsx:587 🖼️ Canvas Image 4: {type: 'image', id: undefined, src: 'http://localhost:3001/api/upload?file=1753819489388-lznjezpv5uk.jpeg', width: 186, height: 272, …}
page.tsx:321 Editor ready in public page: {savePng: ƒ, saveJpg: ƒ, saveSvg: ƒ, saveJson: ƒ, loadJson: ƒ, …}
customization-editor.tsx:1603 🔄 Resetting initial viewport transform to identity matrix
customization-editor.tsx:1607 Editor is ready, checking for content and centering workspace
customization-editor.tsx:2460 Setting up event listeners for download and preview
customization-editor.tsx:2480 Setting up canvas object IDs for editable layers: ['layer_1753820023942']
customization-editor.tsx:2697 🔥 External active layer changed: null
customization-editor.tsx:2698 🔥 Editor canvas available: true
customization-editor.tsx:2699 🔥 Canvas objects count: 8
customization-editor.tsx:2754 Clearing canvas selection
page.tsx:240 🎯 handleLayerActivation called with: null
page.tsx:241 🎯 Previous activeLayerId: null
page.tsx:243 🎯 Setting activeLayerId to: null
customization-editor.tsx:1759 Canvas selection cleared
page.tsx:240 🎯 handleLayerActivation called with: null
page.tsx:241 🎯 Previous activeLayerId: null
page.tsx:243 🎯 Setting activeLayerId to: null
customization-editor.tsx:2466 Cleaning up event listeners
customization-editor.tsx:575 🎨 Editor canvas initialized: {canvasWidth: 933, canvasHeight: 506, objectCount: 8, hasContext: true}
customization-editor.tsx:587 🖼️ Canvas Image 2: {type: 'image', id: undefined, src: 'http://localhost:3001/api/upload?file=1753819456492-5eyhlcm6hpy.jpg', width: 990, height: 1280, …}
customization-editor.tsx:587 🖼️ Canvas Image 3: {type: 'image', id: 'layer_1753820023942', src: 'http://localhost:3001/api/upload?file=1753819967507-yaqjxrz3z3c.png', width: 742, height: 801, …}
customization-editor.tsx:587 🖼️ Canvas Image 4: {type: 'image', id: undefined, src: 'http://localhost:3001/api/upload?file=1753819489388-lznjezpv5uk.jpeg', width: 186, height: 272, …}
page.tsx:321 Editor ready in public page: {savePng: ƒ, saveJpg: ƒ, saveSvg: ƒ, saveJson: ƒ, loadJson: ƒ, …}
customization-editor.tsx:1603 🔄 Resetting initial viewport transform to identity matrix
customization-editor.tsx:1607 Editor is ready, checking for content and centering workspace
customization-editor.tsx:2460 Setting up event listeners for download and preview
customization-editor.tsx:2480 Setting up canvas object IDs for editable layers: ['layer_1753820023942']
customization-editor.tsx:2697 🔥 External active layer changed: null
customization-editor.tsx:2698 🔥 Editor canvas available: true
customization-editor.tsx:2699 🔥 Canvas objects count: 8
customization-editor.tsx:2754 Clearing canvas selection
customization-editor.tsx:1882 Applying customizations: {}
customization-editor.tsx:1883 Template editable layers: [{…}]
customization-editor.tsx:1887 Processing layer layer_1753820023942 (image): undefined
customization-editor.tsx:1888 Layer original value: {"left":1233.9679865976686,"top":369.2814917948255,"width":742,"height":801,"scaleX":0.9703504043126685,"scaleY":0.9703504043126685,"angle":0,"src":"http://localhost:3000/api/upload?file=1753819967507-yaqjxrz3z3c.png","uniqueId":"1234_369_720_777"}
customization-editor.tsx:1889 Custom value type: undefined Original value type: string
customization-editor.tsx:1890 Values equal? false
customization-editor.tsx:1894 Skipping layer layer_1753820023942 - no custom value or same as original
customization-editor.tsx:1882 Applying customizations: {}
customization-editor.tsx:1883 Template editable layers: [{…}]
customization-editor.tsx:1887 Processing layer layer_1753820023942 (image): undefined
customization-editor.tsx:1888 Layer original value: {"left":1233.9679865976686,"top":369.2814917948255,"width":742,"height":801,"scaleX":0.9703504043126685,"scaleY":0.9703504043126685,"angle":0,"src":"http://localhost:3000/api/upload?file=1753819967507-yaqjxrz3z3c.png","uniqueId":"1234_369_720_777"}
customization-editor.tsx:1889 Custom value type: undefined Original value type: string
customization-editor.tsx:1890 Values equal? false
customization-editor.tsx:1894 Skipping layer layer_1753820023942 - no custom value or same as original
customization-editor.tsx:1614 Canvas has 8 objects (3 images), checking for content...
customization-editor.tsx:1624 Canvas has content and all images loaded, centering workspace
customization-editor.tsx:1614 Canvas has 8 objects (3 images), checking for content...
customization-editor.tsx:1624 Canvas has content and all images loaded, centering workspace
customization-editor.tsx:2630 ⚠️ Object already has ID: layer_1753820023942 for layer layer_1753820023942
customization-editor.tsx:2637 Locking non-editable objects...
customization-editor.tsx:2657 🔒 Locked non-editable object: rect (ID: no-id)
customization-editor.tsx:2657 🔒 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:2665 🔓 Enabled interaction for editable object: image (ID: layer_1753820023942)
customization-editor.tsx:2657 🔒 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:2657 🔒 Locked non-editable object: rect (ID: no-id)
customization-editor.tsx:2657 🔒 Locked non-editable object: rect (ID: no-id)
customization-editor.tsx:2657 🔒 Locked non-editable object: textbox (ID: no-id)
customization-editor.tsx:2657 🔒 Locked non-editable object: textbox (ID: no-id)
customization-editor.tsx:2630 ⚠️ Object already has ID: layer_1753820023942 for layer layer_1753820023942
customization-editor.tsx:2637 Locking non-editable objects...
customization-editor.tsx:2657 🔒 Locked non-editable object: rect (ID: no-id)
customization-editor.tsx:2657 🔒 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:2665 🔓 Enabled interaction for editable object: image (ID: layer_1753820023942)
customization-editor.tsx:2657 🔒 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:2657 🔒 Locked non-editable object: rect (ID: no-id)
customization-editor.tsx:2657 🔒 Locked non-editable object: rect (ID: no-id)
customization-editor.tsx:2657 🔒 Locked non-editable object: textbox (ID: no-id)
customization-editor.tsx:2657 🔒 Locked non-editable object: textbox (ID: no-id)
customization-editor.tsx:1473 🔍 centerWorkspace: Found objects: 8 (8) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
customization-editor.tsx:1521 🔄 centerWorkspace: No workspace object found, centering all content
customization-editor.tsx:1545 📐 centerWorkspace: Content bounds: {minX: 706.81, minY: -99.52999999999992, maxX: 1696.69, maxY: 1124, contentWidth: 989.8800000000001, …}
customization-editor.tsx:1554 📐 centerWorkspace: Container dimensions: {containerWidth: 933, containerHeight: 506}
customization-editor.tsx:1576 ✅ centerWorkspace: Applying content-based transform: {scale: 0.38086520150711467, offsetX: 8.795244088824973, offsetY: 57.907513506003056, containerCenter: {…}, contentCenter: {…}, …}
customization-editor.tsx:1587 🎯 Content centered and fitted successfully
customization-editor.tsx:2676 Template loaded successfully, clearing loading state
customization-editor.tsx:2676 Template loaded successfully, clearing loading state
customization-editor.tsx:1465 ✅ centerWorkspace: Already properly centered, skipping...
customization-editor.tsx:602 🔄 Force rendering canvas...
customization-editor.tsx:620 📊 Image loading status: 3/3 images loaded
customization-editor.tsx:602 🔄 Force rendering canvas...
customization-editor.tsx:620 📊 Image loading status: 3/3 images loaded
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:690 🔓 Re-enabled interaction for object: image (ID: layer_1753820023942)
customization-editor.tsx:674 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:690 🔓 Re-enabled interaction for object: image (ID: layer_1753820023942)
customization-editor.tsx:674 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:674 🔒 Re-locked object: textbox (ID: undefined)
