#!/usr/bin/env node

/**
 * cPanel Compatibility Check Script
 * This script checks if the project is ready for cPanel deployment
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Checking cPanel deployment compatibility...\n');

let issues = [];
let warnings = [];

// Check Next.js configuration
console.log('📋 Checking Next.js configuration...');
try {
  const nextConfigPath = 'next.config.mjs';
  if (fs.existsSync(nextConfigPath)) {
    const configContent = fs.readFileSync(nextConfigPath, 'utf8');
    
    if (configContent.includes('output:') && configContent.includes('export')) {
      console.log('✅ Static export configuration found');
    } else {
      warnings.push('Static export configuration not found in next.config.mjs');
    }
    
    if (configContent.includes('unoptimized: true')) {
      console.log('✅ Image optimization disabled for static export');
    } else {
      warnings.push('Image optimization should be disabled for static export');
    }
  } else {
    issues.push('next.config.mjs not found');
  }
} catch (error) {
  issues.push(`Error reading next.config.mjs: ${error.message}`);
}

// Check package.json scripts
console.log('\n📦 Checking package.json scripts...');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  if (packageJson.scripts['build:cpanel']) {
    console.log('✅ cPanel build script found');
  } else {
    issues.push('build:cpanel script not found in package.json');
  }
  
  if (packageJson.scripts['deploy:cpanel']) {
    console.log('✅ cPanel deploy script found');
  } else {
    issues.push('deploy:cpanel script not found in package.json');
  }
  
  if (packageJson.devDependencies['cross-env']) {
    console.log('✅ cross-env dependency found');
  } else {
    issues.push('cross-env dependency not found');
  }
} catch (error) {
  issues.push(`Error reading package.json: ${error.message}`);
}

// Check environment files
console.log('\n🔧 Checking environment configuration...');
if (fs.existsSync('.env.production.example')) {
  console.log('✅ Production environment template found');
} else {
  issues.push('.env.production.example template not found');
}

if (fs.existsSync('.env.production')) {
  console.log('✅ Production environment file found');
  
  // Check for required variables
  try {
    require('dotenv').config({ path: '.env.production' });
    
    if (process.env.NEXT_PUBLIC_APP_URL) {
      console.log('✅ NEXT_PUBLIC_APP_URL configured');
    } else {
      warnings.push('NEXT_PUBLIC_APP_URL not set in .env.production');
    }
    
    if (process.env.CPANEL_DEPLOY) {
      console.log('✅ CPANEL_DEPLOY flag configured');
    } else {
      warnings.push('CPANEL_DEPLOY flag not set in .env.production');
    }
  } catch (error) {
    warnings.push(`Error loading .env.production: ${error.message}`);
  }
} else {
  warnings.push('.env.production not found - run "npm run cpanel:setup" to create it');
}

// Check .htaccess
console.log('\n🌐 Checking Apache configuration...');
if (fs.existsSync('.htaccess')) {
  console.log('✅ .htaccess file found');
  
  const htaccessContent = fs.readFileSync('.htaccess', 'utf8');
  if (htaccessContent.includes('RewriteEngine On')) {
    console.log('✅ URL rewriting enabled');
  } else {
    warnings.push('URL rewriting not enabled in .htaccess');
  }
} else {
  issues.push('.htaccess file not found');
}

// Check for server-side features that won't work with static export
console.log('\n⚠️  Checking for incompatible features...');
const checkForServerFeatures = (dir) => {
  if (!fs.existsSync(dir)) return;
  
  const files = fs.readdirSync(dir, { withFileTypes: true });
  
  files.forEach(file => {
    if (file.isDirectory()) {
      checkForServerFeatures(path.join(dir, file.name));
    } else if (file.name.endsWith('.ts') || file.name.endsWith('.tsx') || file.name.endsWith('.js') || file.name.endsWith('.jsx')) {
      const filePath = path.join(dir, file.name);
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Check for server-side features
      if (content.includes('getServerSideProps')) {
        warnings.push(`getServerSideProps found in ${filePath} - not compatible with static export`);
      }
      
      if (content.includes('getInitialProps')) {
        warnings.push(`getInitialProps found in ${filePath} - not compatible with static export`);
      }
      
      if (content.includes('revalidate') && content.includes('export')) {
        warnings.push(`ISR (revalidate) found in ${filePath} - not compatible with static export`);
      }
    }
  });
};

checkForServerFeatures('src');

// Check database configuration
console.log('\n🗄️  Checking database configuration...');
try {
  const dbConfigPath = 'src/db/drizzle.ts';
  if (fs.existsSync(dbConfigPath)) {
    const dbContent = fs.readFileSync(dbConfigPath, 'utf8');
    if (dbContent.includes('postgres')) {
      warnings.push('PostgreSQL configuration found - most cPanel hosting uses MySQL');
      console.log('⚠️  Consider switching to MySQL for cPanel compatibility');
    }
  }
} catch (error) {
  // Ignore if file doesn't exist
}

// Summary
console.log('\n' + '='.repeat(50));
console.log('📊 COMPATIBILITY CHECK SUMMARY');
console.log('='.repeat(50));

if (issues.length === 0 && warnings.length === 0) {
  console.log('🎉 All checks passed! Your project is ready for cPanel deployment.');
} else {
  if (issues.length > 0) {
    console.log('\n❌ CRITICAL ISSUES (must be fixed):');
    issues.forEach((issue, index) => {
      console.log(`   ${index + 1}. ${issue}`);
    });
  }
  
  if (warnings.length > 0) {
    console.log('\n⚠️  WARNINGS (recommended to fix):');
    warnings.forEach((warning, index) => {
      console.log(`   ${index + 1}. ${warning}`);
    });
  }
  
  if (issues.length > 0) {
    console.log('\n🔧 Please fix the critical issues before deploying.');
    process.exit(1);
  } else {
    console.log('\n✅ No critical issues found. You can proceed with deployment.');
    console.log('💡 Consider addressing the warnings for optimal performance.');
  }
}

console.log('\n📝 Next steps:');
console.log('1. Run "npm run cpanel:setup" if you haven\'t already');
console.log('2. Configure your .env.production file');
console.log('3. Run "npm run build:cpanel" to test the build');
console.log('4. Run "npm run deploy:cpanel" when ready to deploy');
