#!/usr/bin/env node

/**
 * cPanel Setup Script
 * This script helps set up the project for cPanel deployment
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Setting up project for cPanel deployment...\n');

// Check if required files exist
const requiredFiles = [
  '.env.production.example',
  '.htaccess',
  'cpanel.config.js',
  'next.config.mjs'
];

console.log('📋 Checking required files...');
let missingFiles = [];

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} - Found`);
  } else {
    console.log(`❌ ${file} - Missing`);
    missingFiles.push(file);
  }
});

if (missingFiles.length > 0) {
  console.log('\n❌ Missing required files for cPanel deployment:');
  missingFiles.forEach(file => console.log(`   - ${file}`));
  console.log('\nPlease ensure all required files are present before deploying.');
  process.exit(1);
}

// Check if .env.production exists
console.log('\n🔧 Checking environment configuration...');
if (!fs.existsSync('.env.production')) {
  console.log('⚠️  .env.production not found');
  console.log('📝 Creating .env.production from template...');
  
  try {
    const template = fs.readFileSync('.env.production.example', 'utf8');
    fs.writeFileSync('.env.production', template);
    console.log('✅ .env.production created from template');
    console.log('⚠️  Please edit .env.production with your actual values before deploying!');
  } catch (error) {
    console.error('❌ Failed to create .env.production:', error.message);
    process.exit(1);
  }
} else {
  console.log('✅ .env.production found');
}

// Check Node.js version
console.log('\n🔍 Checking Node.js version...');
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);

if (majorVersion < 18) {
  console.log(`⚠️  Node.js ${nodeVersion} detected. Node.js 18+ is recommended for Next.js 14.`);
  console.log('   Most cPanel hosting supports Node.js 18+');
} else {
  console.log(`✅ Node.js ${nodeVersion} - Compatible`);
}

// Check dependencies
console.log('\n📦 Checking dependencies...');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  // Check for cross-env
  if (!packageJson.devDependencies['cross-env']) {
    console.log('⚠️  cross-env not found in devDependencies');
    console.log('📦 Installing cross-env...');
    execSync('npm install --save-dev cross-env', { stdio: 'inherit' });
    console.log('✅ cross-env installed');
  } else {
    console.log('✅ cross-env found');
  }
  
} catch (error) {
  console.error('❌ Failed to check dependencies:', error.message);
}

// Create uploads directory
console.log('\n📁 Setting up directories...');
const uploadsDir = path.join('public', 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
  console.log('✅ Created public/uploads directory');
  
  // Create .gitkeep file
  fs.writeFileSync(path.join(uploadsDir, '.gitkeep'), '');
  console.log('✅ Added .gitkeep to uploads directory');
} else {
  console.log('✅ public/uploads directory exists');
}

console.log('\n🎉 cPanel setup completed successfully!');
console.log('\n📝 Next steps:');
console.log('1. Edit .env.production with your actual values');
console.log('2. Set up your cPanel database and update DB_* variables');
console.log('3. Configure your API keys for external services');
console.log('4. Run "npm run build:cpanel" to test the build');
console.log('5. Run "npm run deploy:cpanel" when ready to deploy');
console.log('\n📚 See CPANEL_DEPLOYMENT.md for detailed instructions.');
