#!/usr/bin/env node

/**
 * cPanel Deployment Script
 * This script builds and prepares the project for cPanel deployment
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Starting cPanel deployment process...\n');

// Check if .env.production exists
if (!fs.existsSync('.env.production')) {
  console.error('❌ .env.production not found!');
  console.log('Please run "npm run cpanel:setup" first or create .env.production manually.');
  process.exit(1);
}

// Load environment variables
require('dotenv').config({ path: '.env.production' });

// Validate required environment variables
const requiredEnvVars = [
  'NEXT_PUBLIC_APP_URL'
];

console.log('🔍 Validating environment variables...');
let missingVars = [];

requiredEnvVars.forEach(varName => {
  if (!process.env[varName]) {
    missingVars.push(varName);
  }
});

if (missingVars.length > 0) {
  console.error('❌ Missing required environment variables:');
  missingVars.forEach(varName => console.log(`   - ${varName}`));
  console.log('\nPlease update your .env.production file.');
  process.exit(1);
}

console.log('✅ Environment variables validated');

// Clean previous build
console.log('\n🧹 Cleaning previous build...');
if (fs.existsSync('out')) {
  fs.rmSync('out', { recursive: true, force: true });
  console.log('✅ Cleaned out directory');
}

if (fs.existsSync('.next')) {
  fs.rmSync('.next', { recursive: true, force: true });
  console.log('✅ Cleaned .next directory');
}

// Build the project
console.log('\n🔨 Building project for cPanel...');
try {
  execSync('npm run build:cpanel', { 
    stdio: 'inherit',
    env: { ...process.env, NODE_ENV: 'production', CPANEL_DEPLOY: 'true' }
  });
  console.log('✅ Build completed successfully');
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}

// Check if out directory was created
if (!fs.existsSync('out')) {
  console.error('❌ Static export failed - out directory not found');
  console.log('Make sure your next.config.mjs is configured for static export');
  process.exit(1);
}

// Copy additional files to out directory
console.log('\n📁 Copying additional files...');

// Copy .htaccess
if (fs.existsSync('.htaccess')) {
  fs.copyFileSync('.htaccess', 'out/.htaccess');
  console.log('✅ Copied .htaccess');
}

// Copy uploads directory if it exists
const uploadsDir = path.join('public', 'uploads');
const outUploadsDir = path.join('out', 'uploads');

if (fs.existsSync(uploadsDir)) {
  if (!fs.existsSync(outUploadsDir)) {
    fs.mkdirSync(outUploadsDir, { recursive: true });
  }
  
  // Copy all files from uploads
  const files = fs.readdirSync(uploadsDir);
  files.forEach(file => {
    const srcFile = path.join(uploadsDir, file);
    const destFile = path.join(outUploadsDir, file);
    if (fs.statSync(srcFile).isFile()) {
      fs.copyFileSync(srcFile, destFile);
    }
  });
  console.log('✅ Copied uploads directory');
}

// Create deployment info file
const deploymentInfo = {
  timestamp: new Date().toISOString(),
  version: require('../package.json').version,
  nodeVersion: process.version,
  environment: 'production',
  deploymentType: 'cpanel-static'
};

fs.writeFileSync('out/deployment-info.json', JSON.stringify(deploymentInfo, null, 2));
console.log('✅ Created deployment info file');

// Generate file list for upload
console.log('\n📋 Generating file list...');
const generateFileList = (dir, baseDir = '') => {
  const files = [];
  const items = fs.readdirSync(dir);
  
  items.forEach(item => {
    const fullPath = path.join(dir, item);
    const relativePath = path.join(baseDir, item);
    
    if (fs.statSync(fullPath).isDirectory()) {
      files.push(...generateFileList(fullPath, relativePath));
    } else {
      files.push(relativePath);
    }
  });
  
  return files;
};

const fileList = generateFileList('out');
fs.writeFileSync('out/file-list.txt', fileList.join('\n'));
console.log(`✅ Generated file list (${fileList.length} files)`);

console.log('\n🎉 Deployment preparation completed successfully!');
console.log('\n📁 Files ready for upload in the "out" directory');
console.log('\n📝 Next steps:');
console.log('1. Upload all files from the "out" directory to your cPanel public_html folder');
console.log('2. If deploying to a subdirectory, upload to public_html/your-subdirectory');
console.log('3. Set up your database using cPanel MySQL Databases');
console.log('4. Update your domain DNS if needed');
console.log('5. Test your deployment at your domain');
console.log('\n📚 See CPANEL_DEPLOYMENT.md for detailed upload instructions.');
