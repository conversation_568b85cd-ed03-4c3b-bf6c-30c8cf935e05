import { NextRequest, NextResponse } from 'next/server'
import { writeFile, mkdir, access, readFile } from 'fs/promises'
import { join } from 'path'
import { auth } from '@/auth'

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const data = await request.formData()
    const file: File | null = data.get('file') as unknown as File

    if (!file) {
      return NextResponse.json({ error: 'No file uploaded' }, { status: 400 })
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      return NextResponse.json({ error: 'Only image files are allowed' }, { status: 400 })
    }

    // Validate file size (4MB limit)
    const maxSize = 4 * 1024 * 1024 // 4MB
    if (file.size > maxSize) {
      return NextResponse.json({ error: 'File size too large. Maximum 4MB allowed.' }, { status: 400 })
    }

    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // Create uploads directory if it doesn't exist
    const uploadsDir = join(process.cwd(), 'public', 'uploads')
    try {
      await mkdir(uploadsDir, { recursive: true })
    } catch (error) {
      // Directory might already exist
    }

    // Generate deterministic filename for thumbnails, or unique filename for other files
    let filename: string;
    const extension = file.name.split('.').pop() || 'jpg';

    // Check if this is a thumbnail upload (deterministic naming)
    if (file.name.startsWith('thumbnail-') && file.name.includes('.')) {
      // Use the provided filename for thumbnails (e.g., "thumbnail-{projectId}.jpg")
      filename = file.name;
    } else {
      // Generate unique filename for non-thumbnail uploads
      const timestamp = Date.now();
      const randomString = Math.random().toString(36).substring(2, 15);
      filename = `${timestamp}-${randomString}.${extension}`;
    }
    
    const filepath = join(uploadsDir, filename)

    // For thumbnail files, check if file exists and log replacement
    if (file.name.startsWith('thumbnail-')) {
      try {
        await access(filepath);
        console.log(`🔄 Replacing existing thumbnail: ${filename}`);
      } catch {
        console.log(`📁 Creating new thumbnail: ${filename}`);
      }
    }

    // Write file (this will overwrite existing files)
    await writeFile(filepath, buffer)
    
    // Return the public URL
    const url = `/uploads/${filename}`
    
    return NextResponse.json({ 
      url,
      name: file.name,
      size: file.size,
      type: file.type
    })

  } catch (error) {
    // Upload error - logging removed
    return NextResponse.json({ error: 'Upload failed' }, { status: 500 })
  }
}

// GET handler to serve uploaded files with CORS headers
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const filename = searchParams.get('file')

    if (!filename) {
      return NextResponse.json({ error: 'No filename provided' }, { status: 400 })
    }

    // Security: Only allow files from uploads directory and prevent path traversal
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      return NextResponse.json({ error: 'Invalid filename' }, { status: 400 })
    }

    const uploadsDir = join(process.cwd(), 'public', 'uploads')
    const filepath = join(uploadsDir, filename)

    try {
      // Check if file exists
      await access(filepath)

      // Read the file
      const fileBuffer = await readFile(filepath)

      // Determine content type based on file extension
      const ext = filename.toLowerCase().split('.').pop()
      let contentType = 'application/octet-stream'

      switch (ext) {
        case 'jpg':
        case 'jpeg':
          contentType = 'image/jpeg'
          break
        case 'png':
          contentType = 'image/png'
          break
        case 'gif':
          contentType = 'image/gif'
          break
        case 'webp':
          contentType = 'image/webp'
          break
        case 'svg':
          contentType = 'image/svg+xml'
          break
      }

      // Return the file with CORS headers
      return new NextResponse(fileBuffer, {
        status: 200,
        headers: {
          'Content-Type': contentType,
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET',
          'Access-Control-Allow-Headers': 'Content-Type',
          'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
        },
      })

    } catch (fileError) {
      return NextResponse.json({ error: 'File not found' }, { status: 404 })
    }

  } catch (error) {
    return NextResponse.json({ error: 'Failed to serve file' }, { status: 500 })
  }
}
