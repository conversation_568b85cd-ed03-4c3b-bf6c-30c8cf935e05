'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function TestDownloadPage() {
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [isDownloading, setIsDownloading] = useState(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Generate a test preview image
  useEffect(() => {
    const generateTestPreview = () => {
      const canvas = canvasRef.current;
      if (!canvas) return;

      const ctx = canvas.getContext('2d');
      if (!ctx) return;

      // Set canvas size
      canvas.width = 800;
      canvas.height = 600;

      // Draw background
      ctx.fillStyle = '#f8f9fa';
      ctx.fillRect(0, 0, 800, 600);

      // Draw border
      ctx.strokeStyle = '#dee2e6';
      ctx.lineWidth = 2;
      ctx.strokeRect(10, 10, 780, 580);

      // Draw title
      ctx.fillStyle = '#212529';
      ctx.font = 'bold 48px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('Test Download', 400, 150);

      // Draw subtitle
      ctx.font = '24px Arial';
      ctx.fillStyle = '#6c757d';
      ctx.fillText('Customized Template Preview', 400, 200);

      // Draw some shapes
      ctx.fillStyle = '#007bff';
      ctx.fillRect(150, 250, 200, 100);

      ctx.fillStyle = '#28a745';
      ctx.beginPath();
      ctx.arc(550, 300, 50, 0, 2 * Math.PI);
      ctx.fill();

      // Draw text
      ctx.fillStyle = '#dc3545';
      ctx.font = '20px Arial';
      ctx.textAlign = 'left';
      ctx.fillText('Sample customized content', 150, 400);
      ctx.fillText('High-quality PNG export', 150, 430);
      ctx.fillText('Working download functionality', 150, 460);

      // Generate data URL
      const dataUrl = canvas.toDataURL('image/png', 1.0);
      setPreviewUrl(dataUrl);
    };

    generateTestPreview();
  }, []);

  // Test the download functionality (same as implemented in the main app)
  const testDownload = async () => {
    setIsDownloading(true);
    try {
      // Simulate the same process as the main app
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (previewUrl) {
        const link = document.createElement('a');
        link.href = previewUrl;
        link.download = 'test-customized-design.png';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        console.log('✅ Download test successful!');
      } else {
        console.error('❌ No preview available for download');
      }
    } catch (err) {
      console.error('❌ Download test failed:', err);
    } finally {
      setIsDownloading(false);
    }
  };

  // Test the CustomEvent system (same as current implementation)
  const testCustomEventDownload = async () => {
    setIsDownloading(true);
    try {
      const event = new CustomEvent('downloadCustomized', {
        detail: {
          filename: 'test-custom-event-download.png',
          quality: 1.0,
          format: 'png'
        }
      });

      // Set up a temporary listener to handle the event
      const handleDownloadEvent = (event: CustomEvent) => {
        console.log('📨 CustomEvent received:', event.detail);
        
        if (previewUrl) {
          const link = document.createElement('a');
          link.href = previewUrl;
          link.download = event.detail.filename;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          console.log('✅ CustomEvent download successful!');
        }
      };

      window.addEventListener('downloadCustomized', handleDownloadEvent as EventListener);
      window.dispatchEvent(event);
      
      // Clean up
      setTimeout(() => {
        window.removeEventListener('downloadCustomized', handleDownloadEvent as EventListener);
      }, 100);

      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (err) {
      console.error('❌ CustomEvent download failed:', err);
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Download Functionality Test
          </h1>
          <p className="text-gray-600">
            Testing the download implementation from src1/ vs current project
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Preview Panel */}
          <Card>
            <CardHeader>
              <CardTitle>Generated Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <canvas
                  ref={canvasRef}
                  className="hidden"
                />
                {previewUrl && (
                  <div className="border rounded-lg overflow-hidden">
                    <img
                      src={previewUrl}
                      alt="Test preview"
                      className="w-full h-auto"
                    />
                  </div>
                )}
                <div className="text-sm text-gray-600">
                  <p>Preview URL length: {previewUrl.length} characters</p>
                  <p>Format: PNG, Quality: 1.0</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Test Controls */}
          <Card>
            <CardHeader>
              <CardTitle>Download Tests</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold mb-2">Method 1: Direct Download (src1/ approach)</h3>
                  <p className="text-sm text-gray-600 mb-3">
                    Simple and reliable - downloads the preview URL directly
                  </p>
                  <Button
                    onClick={testDownload}
                    disabled={isDownloading || !previewUrl}
                    className="w-full"
                  >
                    {isDownloading ? 'Downloading...' : 'Test Direct Download'}
                  </Button>
                </div>

                <div>
                  <h3 className="font-semibold mb-2">Method 2: CustomEvent System</h3>
                  <p className="text-sm text-gray-600 mb-3">
                    Current project approach - uses CustomEvent dispatch
                  </p>
                  <Button
                    onClick={testCustomEventDownload}
                    disabled={isDownloading || !previewUrl}
                    variant="outline"
                    className="w-full"
                  >
                    {isDownloading ? 'Downloading...' : 'Test CustomEvent Download'}
                  </Button>
                </div>

                <div className="pt-4 border-t">
                  <h3 className="font-semibold mb-2">Test Results</h3>
                  <p className="text-sm text-gray-600">
                    Check the browser console for detailed test results and download status.
                    Both methods should successfully download the test image.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Implementation Comparison</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-green-600 mb-2">✅ src1/ Approach (Working)</h3>
                <ul className="text-sm space-y-1 text-gray-600">
                  <li>• Simple and direct</li>
                  <li>• Uses previewUrl state</li>
                  <li>• Reliable download trigger</li>
                  <li>• Less complex code</li>
                  <li>• Easier to debug</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold text-blue-600 mb-2">🔄 Current Project Approach</h3>
                <ul className="text-sm space-y-1 text-gray-600">
                  <li>• CustomEvent system</li>
                  <li>• Advanced canvas cropping</li>
                  <li>• More complex logic</li>
                  <li>• Event-driven architecture</li>
                  <li>• Potential timing issues</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
