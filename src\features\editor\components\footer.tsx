import { Minimize, ZoomIn, ZoomOut, MousePointer2, <PERSON>, Sciss<PERSON> } from "lucide-react";

import { Editor, ActiveTool } from "@/features/editor/types";

import { Hint } from "@/components/hint";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface FooterProps {
  editor: Editor | undefined;
  activeTool: ActiveTool;
  onChangeActiveTool: (tool: ActiveTool) => void;
};

export const Footer = ({ editor, activeTool, onChangeActiveTool }: FooterProps) => {
  return (
    <footer className="h-[52px] border-t bg-white w-full flex items-center justify-between overflow-x-auto z-[49] p-2 gap-x-1 shrink-0 px-4">
      {/* Tool Selection - Left Side */}
      <div className="flex items-center gap-x-1">
        <Hint label="Select" side="top" sideOffset={10}>
          <Button
            onClick={() => onChangeActiveTool("select")}
            size="icon"
            variant="ghost"
            className={cn(
              "h-full",
              activeTool === "select" && "bg-gray-100"
            )}
          >
            <MousePointer2 className="size-4" />
          </Button>
        </Hint>
        <Hint label="Pan" side="top" sideOffset={10}>
          <Button
            onClick={() => onChangeActiveTool("pan")}
            size="icon"
            variant="ghost"
            className={cn(
              "h-full",
              activeTool === "pan" && "bg-gray-100"
            )}
          >
            <Hand className="size-4" />
          </Button>
        </Hint>
        <Hint label="Remove Background" side="top" sideOffset={10}>
          <Button
            onClick={() => onChangeActiveTool("remove-bg")}
            size="icon"
            variant="ghost"
            className={cn(
              "h-full",
              activeTool === "remove-bg" && "bg-gray-100"
            )}
          >
            <Scissors className="size-4" />
          </Button>
        </Hint>
      </div>

      {/* Zoom Controls - Right Side */}
      <div className="flex items-center gap-x-1">
        <Hint label="Zoom out" side="top" sideOffset={10}>
          <Button
            onClick={() => editor?.zoomOut()}
            size="icon"
            variant="ghost"
            className="h-full"
          >
            <ZoomOut className="size-4" />
          </Button>
        </Hint>
        <Hint label="Zoom in" side="top" sideOffset={10}>
          <Button
            onClick={() => editor?.zoomIn()}
            size="icon"
            variant="ghost"
            className="h-full"
          >
            <ZoomIn className="size-4" />
          </Button>
        </Hint>
        <Hint label="Reset" side="top" sideOffset={10}>
          <Button
            onClick={() => {
              // Reset and center workspace
              if (editor?.canvas) {
                const objects = editor.canvas.getObjects();
                const workspace = objects.find((obj: any) => obj.name === 'clip');

                if (workspace) {
                  const canvasWidth = editor.canvas.getWidth();
                  const canvasHeight = editor.canvas.getHeight();

                  // Calculate scale to fit workspace with padding
                  const padding = 40;
                  const availableWidth = canvasWidth - padding;
                  const availableHeight = canvasHeight - padding;

                  const scaleX = availableWidth / (workspace.width || 1);
                  const scaleY = availableHeight / (workspace.height || 1);
                  const scale = Math.min(scaleX, scaleY, 1);

                  // Center the workspace
                  const workspaceCenter = workspace.getCenterPoint();
                  const canvasCenter = { left: canvasWidth / 2, top: canvasHeight / 2 };

                  const offsetX = canvasCenter.left - workspaceCenter.x * scale;
                  const offsetY = canvasCenter.top - workspaceCenter.y * scale;

                  editor.canvas.setViewportTransform([scale, 0, 0, scale, offsetX, offsetY]);
                  editor.canvas.renderAll();
                } else {
                  // Fallback: simple reset
                  editor.canvas.setZoom(1);
                  editor.canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);
                  editor.canvas.renderAll();
                }
              }
            }}
            size="icon"
            variant="ghost"
            className="h-full"
          >
            <Minimize className="size-4" />
          </Button>
        </Hint>
      </div>
    </footer>
  );
};
