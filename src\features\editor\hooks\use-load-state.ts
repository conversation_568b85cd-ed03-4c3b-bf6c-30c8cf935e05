import { fabric } from "fabric";
import { useEffect, useRef } from "react";

import { JSON_KEYS } from "@/features/editor/types";

interface UseLoadStateProps {
  canvas: fabric.Canvas | null;
  initialState: React.MutableRefObject<string | undefined>;
  canvasHistory: React.MutableRefObject<string[]>;
  setHistoryIndex: React.Dispatch<React.SetStateAction<number>>;
};

export const useLoadState = ({
  canvas,
  initialState,
  canvasHistory,
  setHistoryIndex,
}: UseLoadStateProps) => {
  const initialized = useRef(false);

  useEffect(() => {
    if (!initialized.current && initialState?.current && canvas) {
      console.log('useLoadState: Attempting to load initial state');
      try {
        const data = JSON.parse(initialState.current);
        console.log('useLoadState: Parsed JSON data:', {
          objectCount: data.objects?.length || 0,
          version: data.version
        });

        // Validate canvas dimensions before loading
        const canvasWidth = canvas.getWidth();
        const canvasHeight = canvas.getHeight();

        console.log('useLoadState: Canvas dimensions:', { canvasWidth, canvasHeight });

        if (canvasWidth <= 0 || canvasHeight <= 0) {
          console.warn("Canvas has invalid dimensions, skipping loadFromJSON", { canvasWidth, canvasHeight });
          return;
        }

        console.log('useLoadState: Loading JSON into canvas...');
        canvas.loadFromJSON(data, () => {
          console.log('useLoadState: JSON loaded successfully, objects count:', canvas.getObjects().length);

          // Ensure all objects have valid dimensions after loading
          canvas.getObjects().forEach(obj => {
            if (obj.width === 0 || obj.height === 0) {
              console.warn("Object has zero dimensions after loading:", obj);
              // Set minimum dimensions to prevent rendering errors
              if (obj.width === 0) obj.set('width', 1);
              if (obj.height === 0) obj.set('height', 1);
            }

            // Fix invalid textBaseline values
            if (obj.type === 'textbox' || obj.type === 'text' || obj.type === 'i-text') {
              const textObj = obj as fabric.Text;
              const currentBaseline = (textObj as any).textBaseline;
              const validBaselines = ['top', 'hanging', 'middle', 'alphabetic', 'ideographic', 'bottom'];

              // Fix common misspelling 'alphabetical' -> 'alphabetic'
              if (currentBaseline === 'alphabetical') {
                console.warn(`Fixing misspelled textBaseline "alphabetical" to "alphabetic"`);
                (textObj as any).textBaseline = 'alphabetic';
              } else if (currentBaseline && !validBaselines.includes(currentBaseline)) {
                console.warn(`Invalid textBaseline value "${currentBaseline}" found, setting to "middle"`);
                (textObj as any).textBaseline = 'middle';
              }
            }
          });

          const currentState = JSON.stringify(
            canvas.toJSON(JSON_KEYS),
          );

          canvasHistory.current = [currentState];
          setHistoryIndex(0);

          console.log('useLoadState: Template loading complete');
        });
        initialized.current = true;
      } catch (error) {
        console.error("Error loading canvas state:", error);
        initialized.current = true; // Prevent infinite retries
      }
    }
  },
  [
    canvas,
    initialState, // no need, this is a ref
    canvasHistory, // no need, this is a ref
    setHistoryIndex, // no need, this is a dispatch
  ]);
};
