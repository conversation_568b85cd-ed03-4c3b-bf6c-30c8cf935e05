import { useCallback, useRef } from "react";
import { useQueryClient } from "@tanstack/react-query";

import { Editor } from "@/features/editor/types";
import { thumbnailManager } from "@/features/editor/utils/thumbnail-manager";

interface UseThumbnailGeneratorProps {
  editor: Editor | undefined;
  projectId: string;
  onThumbnailGenerated?: (thumbnailUrl: string) => void;
}

/**
 * New thumbnail generation hook that only generates thumbnails on:
 * 1. Explicit saves (manual or auto-save)
 * 2. Missing thumbnails
 * 3. Never on routine canvas interactions
 */
export const useThumbnailGenerator = ({
  editor,
  projectId,
  onThumbnailGenerated,
}: UseThumbnailGeneratorProps) => {
  const queryClient = useQueryClient();
  const isGeneratingRef = useRef(false);

  /**
   * Generate thumbnail only on explicit save
   * This replaces the old debounced generation that triggered on every canvas change
   */
  const generateThumbnailOnSave = useCallback(async () => {
    if (!editor) {
      console.warn("No editor available for thumbnail generation");
      return null;
    }

    if (!editor.canvas) {
      console.warn("No canvas available for thumbnail generation");
      return null;
    }

    if (isGeneratingRef.current) {
      console.log("Thumbnail generation already in progress, skipping...");
      return null;
    }

    isGeneratingRef.current = true;

    try {
      console.log(`📸 Generating thumbnail on save for project: ${projectId}`);

      const thumbnailUrl = await thumbnailManager.generateAndSaveThumbnail(
        editor,
        projectId,
        {
          width: 300,
          height: 200,
          quality: 0.7,
          format: "image/jpeg",
        }
      );

      if (thumbnailUrl) {
        console.log(`✅ Thumbnail generated successfully: ${thumbnailUrl}`);
        onThumbnailGenerated?.(thumbnailUrl);

        // Invalidate queries to refresh UI with force refresh
        await queryClient.invalidateQueries({ queryKey: ["projects"] });
        await queryClient.invalidateQueries({ queryKey: ["project", projectId] });

        // Force refetch to ensure UI updates immediately
        await queryClient.refetchQueries({ queryKey: ["projects"] });
        await queryClient.refetchQueries({ queryKey: ["project", projectId] });
      }

      return thumbnailUrl;
    } catch (error) {
      console.error("Error generating thumbnail on save:", error);
      return null;
    } finally {
      isGeneratingRef.current = false;
    }
  }, [editor, projectId, onThumbnailGenerated, queryClient]);

  /**
   * Check if a project needs a thumbnail and generate one if missing
   * This is called when the editor loads to ensure thumbnails exist
   */
  const generateThumbnailIfMissing = useCallback(async () => {
    if (!editor?.canvas || isGeneratingRef.current) {
      return null;
    }

    try {
      const needsThumbnail = await thumbnailManager.needsThumbnail(projectId);

      if (needsThumbnail) {
        console.log(`🔍 Project ${projectId} needs a thumbnail, generating...`);
        return await generateThumbnailOnSave();
      } else {
        console.log(`✅ Project ${projectId} already has a valid thumbnail`);
        return null;
      }
    } catch (error) {
      console.error("Error checking if thumbnail is needed:", error);
      return null;
    }
  }, [editor, projectId, generateThumbnailOnSave]);

  return {
    generateThumbnailOnSave,
    generateThumbnailIfMissing,
  };
};
