/**
 * Test script to verify the download functionality works correctly
 * This script tests the key components of the download system
 */

// Test 1: Verify the download function exists and works
function testDownloadFunction() {
  console.log('🧪 Testing download functionality...');
  
  // Create a test canvas element
  const canvas = document.createElement('canvas');
  canvas.width = 800;
  canvas.height = 600;
  const ctx = canvas.getContext('2d');
  
  // Draw some test content
  ctx.fillStyle = '#f0f0f0';
  ctx.fillRect(0, 0, 800, 600);
  ctx.fillStyle = '#333';
  ctx.font = '48px Arial';
  ctx.textAlign = 'center';
  ctx.fillText('Test Download', 400, 300);
  
  // Convert to data URL
  const dataUrl = canvas.toDataURL('image/png', 1.0);
  
  console.log('✅ Canvas created and converted to dataURL');
  console.log('📊 DataURL length:', dataUrl.length);
  
  return dataUrl;
}

// Test 2: Verify download trigger works
function testDownloadTrigger(dataUrl) {
  console.log('🧪 Testing download trigger...');
  
  try {
    const link = document.createElement('a');
    link.href = dataUrl;
    link.download = 'test-download.png';
    
    // Don't actually trigger download in test, just verify setup
    console.log('✅ Download link created successfully');
    console.log('📁 Filename:', link.download);
    console.log('🔗 Data URL set:', link.href.substring(0, 50) + '...');
    
    return true;
  } catch (error) {
    console.error('❌ Download trigger failed:', error);
    return false;
  }
}

// Test 3: Verify the CustomEvent system works
function testCustomEventSystem() {
  console.log('🧪 Testing CustomEvent system...');
  
  let eventReceived = false;
  
  // Set up event listener
  const handleDownloadEvent = (event) => {
    console.log('✅ Download event received:', event.detail);
    eventReceived = true;
  };
  
  window.addEventListener('downloadCustomized', handleDownloadEvent);
  
  // Dispatch test event
  const event = new CustomEvent('downloadCustomized', {
    detail: {
      filename: 'test-download.png',
      quality: 1.0,
      format: 'png'
    }
  });
  
  window.dispatchEvent(event);
  
  // Clean up
  window.removeEventListener('downloadCustomized', handleDownloadEvent);
  
  if (eventReceived) {
    console.log('✅ CustomEvent system working correctly');
    return true;
  } else {
    console.error('❌ CustomEvent system failed');
    return false;
  }
}

// Run all tests
function runDownloadTests() {
  console.log('🚀 Starting download functionality tests...\n');
  
  const dataUrl = testDownloadFunction();
  console.log('');
  
  const downloadTriggerWorks = testDownloadTrigger(dataUrl);
  console.log('');
  
  const customEventWorks = testCustomEventSystem();
  console.log('');
  
  console.log('📋 Test Results:');
  console.log('- Canvas to DataURL:', '✅ PASS');
  console.log('- Download Trigger:', downloadTriggerWorks ? '✅ PASS' : '❌ FAIL');
  console.log('- CustomEvent System:', customEventWorks ? '✅ PASS' : '❌ FAIL');
  
  const allTestsPassed = downloadTriggerWorks && customEventWorks;
  console.log('\n🎯 Overall Result:', allTestsPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED');
  
  return allTestsPassed;
}

// Make function available globally for browser console testing
if (typeof window !== 'undefined') {
  window.runDownloadTests = runDownloadTests;
  console.log('💡 Run "runDownloadTests()" in the browser console to test download functionality');
}

// Export for Node.js testing if needed
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runDownloadTests, testDownloadFunction, testDownloadTrigger, testCustomEventSystem };
}
